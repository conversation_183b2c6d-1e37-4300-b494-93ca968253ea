#include "interface.h"

void delay_init(void)
{
   SysTick->CTRL&=0xfffffffb;//控制寄存器中选择外部时钟即系统时钟的八分之一（HCLK/8）72M/8=9M。
}

//1us 延时函数
void Delay_us(u32 Nus)   
{   
SysTick->LOAD=Nus*9;          //时间加载    72M分频     
SysTick->CTRL|=0x01;             //开始倒数      
while(!(SysTick->CTRL&(1<<16))); //等待时间到达   
SysTick->CTRL=0X00000000;        //关闭计数器   
SysTick->VAL=0X00000000;         //清空计数器        
} 

void Delayms(u32 Nms)
{
	while(Nms--)
	{
		Delay_us(1000);
	}
}

//使能所有GPIO时钟
void GPIOCLKInit(void)
{
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA , ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB , ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC , ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOD , ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOE , ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOF , ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOG , ENABLE);
}

void UserLEDInit(void)
{
  GPIO_InitTypeDef  GPIO_InitStructure;
	
	GPIO_InitStructure.GPIO_Pin = LED_PIN;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;//
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;//设置GPIO端口速度
	GPIO_Init(LED_GPIO , &GPIO_InitStructure);
	
	LED_SET;	
}

//红外光电对管初始化
void RedRayInit(void)
{
	GPIO_InitTypeDef  GPIO_InitStructure;
	
	GPIO_InitStructure.GPIO_Pin = SEARCH_M_PIN;//设置使用GPIO管脚
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;//设置GPIO模式,上拉输入
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;//设置GPIO端口速度
	GPIO_Init(SEARCH_M_GPIO , &GPIO_InitStructure); 
	
	GPIO_InitStructure.GPIO_Pin = SEARCH_R_PIN;//设置使用GPIO管脚
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;//设置GPIO模式,上拉输入
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;//设置GPIO端口速度
	GPIO_Init(SEARCH_R_GPIO , &GPIO_InitStructure); 
	
	GPIO_InitStructure.GPIO_Pin = SEARCH_L_PIN;//设置使用GPIO管脚
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;//设置GPIO模式,上拉输入
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;//设置GPIO端口速度
	GPIO_Init(SEARCH_L_GPIO , &GPIO_InitStructure); 
}


/**-------------------------------------------------------
  * @函数名 NVIC_TIM5Configuration
  * @功能   配置TIM5中断优先级分组和优先级
  * @参数   无
  * @返回值 无
***------------------------------------------------------*/
static void NVIC_TIM2Configuration(void)
{ 
    NVIC_InitTypeDef NVIC_InitStructure;

    /* Set the Vector Table base address at 0x08000000 */
    //NVIC_SetVectorTable(NVIC_VectTab_FLASH, 0x0000);

    /* Enable the TIM5 gloabal Interrupt */
    NVIC_InitStructure.NVIC_IRQChannel = TIM2_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;

    NVIC_Init(&NVIC_InitStructure);
}


void TIM2_Init(void)
{
    TIM_TimeBaseInitTypeDef  TIM_TimeBaseStructure;

    /* TIM2 clock enable */
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);

    /* Time base configuration */
    //定时器自动重装载的计数值，由于计数是从0开始的，所以为100us
    TIM_TimeBaseStructure.TIM_Period = (100 - 1);//10kHz
    // 定时器预分频系数，当等于0时，表示不分频，需要+1
    TIM_TimeBaseStructure.TIM_Prescaler = (72 - 1);//1MHz
    // 高级应用本参数不涉及，定义在定时时钟(CK_INT)频率与数字滤波器(ETR,TIx)
    // 使用的采样频率之间的分频比例
    TIM_TimeBaseStructure.TIM_ClockDivision = 0;
    //向上计数
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    //初始化定时器5
    TIM_TimeBaseInit(TIM2, &TIM_TimeBaseStructure);

    /* Clear TIM5 update pending flag[清除TIM5更新中断标志] */
    TIM_ClearITPendingBit(TIM2, TIM_IT_Update);

    /* TIM IT enable */ //开启中断
    TIM_ITConfig(TIM2, TIM_IT_Update, ENABLE);

    /* TIM5 enable counter */
    TIM_Cmd(TIM2, ENABLE);  //定时器使能，开始计数

    /* 中断参数配置 */
    NVIC_TIM2Configuration();
}

void LEDToggle(uint16_t Led)
{
    /* 指定管脚输出取反 1变0，实现对应的LED指示灯状态取反的目的 */
	LED_GPIO->ODR ^= Led;
	//如要高效率，可以直接调用 LEDnOBB = !LEDnOBB;
}
